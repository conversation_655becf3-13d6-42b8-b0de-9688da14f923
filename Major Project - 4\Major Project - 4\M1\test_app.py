from flask import Flask, render_template, request, jsonify
import os

app = Flask(__name__)

@app.route('/')
def landing():
    return render_template('landing.html')

@app.route('/analysis')
def analysis():
    return render_template('new_index.html')

@app.route('/models')
def models():
    return render_template('models.html')

@app.route('/chatbot')
def chatbot():
    return render_template('chatbot.html')

@app.route('/test')
def test():
    return jsonify({'status': 'Flask app is working!', 'message': 'All basic routes are functional'})

if __name__ == '__main__':
    print("Starting Test Flask Application...")
    print("Available routes:")
    print("- / (landing page)")
    print("- /analysis")
    print("- /models") 
    print("- /chatbot")
    print("- /test (API test)")
    app.run(debug=True, host='0.0.0.0', port=5000)
